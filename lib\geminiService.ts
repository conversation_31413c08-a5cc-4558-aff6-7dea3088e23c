import { GoogleGenAI, Type } from "@google/genai";
import { RiasecScores, OceanScores } from './types';



// Interface untuk response gabungan RIASEC + OCEAN
export interface CombinedProfileResponse {
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workStyle: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
}



// Fungsi untuk membuat prompt gabungan RIASEC + OCEAN
function createCombinedPrompt(riasecScores: RiasecScores, oceanScores: OceanScores): string {
  // Konversi skor RIASEC dari skala 5-25 ke skala 1-100
  const convertedRiasec = {
    R: Math.round((riasecScores.R / 25) * 100),
    I: Math.round((riasecScores.I / 25) * 100),
    A: Math.round((riasecScores.A / 25) * 100),
    S: Math.round((riasecScores.S / 25) * 100),
    E: Math.round((riasecScores.E / 25) * 100),
    C: Math.round((riasecScores.C / 25) * 100)
  };

  // Konversi skor OCEAN dari skala 5-25 ke skala 1-100
  const convertedOcean = {
    O: Math.round((oceanScores.O / 25) * 100),
    C: Math.round((oceanScores.C / 25) * 100),
    E: Math.round((oceanScores.E / 25) * 100),
    A: Math.round((oceanScores.A / 25) * 100),
    N: Math.round((oceanScores.N / 25) * 100)
  };

  return `# ROLE
You are an expert-level Career Analytics Engine. Your specialization is synthesizing RIASEC (Holland Codes) and Big Five (OCEAN) personality data into a cohesive, objective, and data-driven career profile.

# CONTEXT
You are operating as the core logic for a web application providing career guidance to high school students. The analysis must be objective, analytical, and framed like a clinical report. It should focus on strengths and identify challenges as areas to de-emphasize, not as personal failings. The final output must be a single, valid JSON object and nothing else.

# INPUT DATA
- **RIASEC SCORES (Career Interests - 1-100 scale):**
  - R (Realistic): ${convertedRiasec.R}
  - I (Investigative): ${convertedRiasec.I}
  - A (Artistic): ${convertedRiasec.A}
  - S (Social): ${convertedRiasec.S}
  - E (Enterprising): ${convertedRiasec.E}
  - C (Conventional): ${convertedRiasec.C}
- **BIG FIVE/OCEAN SCORES (Personality Traits - 1-100 scale):**
  - O (Openness): ${convertedOcean.O}
  - C (Conscientiousness): ${convertedOcean.C}
  - E (Extraversion): ${convertedOcean.E}
  - A (Agreeableness): ${convertedOcean.A}
  - N (Neuroticism): ${convertedOcean.N}

# TASK
Your sole task is to analyze the provided RIASEC and OCEAN scores and generate a single JSON object that conforms to the schema provided in the "OUTPUT FORMAT" section. You will accomplish this by following the "THOUGHT PROCESS" outlined below.

# THOUGHT PROCESS (Internal Monologue - Do NOT show in output)
You will follow this 4-step reasoning process internally before constructing the final JSON output.

### Step 1: DECONSTRUCT
- Identify the top 2-3 highest-scoring RIASEC codes. These are the primary career interest drivers.
- Identify the most significant OCEAN scores (both high and low, e.g., >70 or <30). These traits are the "how" and "why" behind the user's behavior.
- Briefly note the core definition of each identified code and trait.

### Step 2: DIAGNOSE
- Synthesize the models. Find the connections, synergies, and tensions between the primary RIASEC interests and the significant OCEAN traits.
- **Synergy Example:** High 'I' (Investigative) + High 'O' (Openness) suggests a strong fit for cutting-edge research and theoretical exploration.
- **Tension Example:** High 'S' (Social) + Low 'E' (Extraversion) suggests a preference for helping others in one-on-one or small-group settings rather than large-scale public engagement.
- Based on the user's request, low scores indicate challenges or areas of less natural inclination. Formulate "development areas" by identifying how a low OCEAN score might create friction with a high RIASEC interest (e.g., High 'E' (Enterprising) + Low 'A' (Agreeableness) might be a challenge in team-based sales). Frame these insights objectively.

### Step 3: DEVELOP
- Based on the synthesis in Step 2, generate the core content for each field in the JSON schema.
- Brainstorm 5 strengths that emerge from the strongest synergies.
- Brainstorm 5 career suggestions that are a strong match for BOTH the interests (RIASEC) and the personality (OCEAN).
- Define the ideal work style.
- Formulate the development areas from the identified tensions.
- Extract specific personality insights from the unique combination.
- Write a concise "careerFit" explanation that ties the core RIASEC/OCEAN combo to the suggested careers.

### Step 4: DELIVER
- Construct the final JSON object.
- Populate each field strictly according to the content developed in Step 3.
- Ensure every string conforms to the language and length requirements specified in the JSON schema descriptions.
- The final output MUST BE ONLY THE JSON. No introductory text, no markdown backticks, just the raw JSON object.

# OUTPUT FORMAT
Your final output must be a single, valid JSON object matching this exact schema. Pay close attention to the descriptions, which contain constraints on content and length.`;
}



// Schema untuk response gabungan RIASEC + OCEAN
const combinedProfileResponseSchema = {
  type: Type.OBJECT,
  properties: {
    profileTitle: {
      type: Type.STRING,
      description: "Judul profil yang menggabungkan insights RIASEC dan OCEAN (Dengan Bahasa inggris yang singkat)"
    },
    profileDescription: {
      type: Type.STRING,
      description: "Deskripsi kepribadian dalam DUA kalimat yang mengintegrasikan kedua model 2 kalimat singkat"
    },
    strengths: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 kekuatan utama berdasarkan kombinasi RIASEC dan OCEAN dalam SATU kalimat singkat"
    },
    careerSuggestions: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 rekomendasi karier yang mempertimbangkan minat dan kepribadian (use english)"
    },
    workStyle: {
      type: Type.STRING,
      description: "Gaya kerja ideal berdasarkan kombinasi kedua profil dalam DUA kalimat singkat"
    },
    developmentAreas: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 area pengembangan berdasarkan analisis gabungan dalam DUA kalimat singkat"
    },
    personalityInsights: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 insights kepribadian spesifik dari kombinasi OCEAN dan RIASEC dalam DUA kalimat singkat"
    },
    careerFit: {
      type: Type.STRING,
      description: "Penjelasan mengapa kombinasi profil ini cocok untuk karier yang direkomendasikan dalam DUA Kalimat singkat"
    }
  },
  propertyOrdering: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle", "developmentAreas", "personalityInsights", "careerFit"],
  required: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle", "developmentAreas", "personalityInsights", "careerFit"]
};

// Service class untuk Gemini AI dengan structured output
export class GeminiProfileService {
  private ai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('NEXT_PUBLIC_GEMINI_API_KEY is not set in environment variables');
    }

    this.ai = new GoogleGenAI({
      apiKey: apiKey
    });
  }



  // Method baru untuk analisis gabungan RIASEC + OCEAN
  async generateCombinedProfile(riasecScores: RiasecScores, oceanScores: OceanScores): Promise<CombinedProfileResponse> {
    try {
      const prompt = createCombinedPrompt(riasecScores, oceanScores);

      // Menggunakan structured output dengan schema gabungan
      const response = await this.ai.models.generateContent({
        model: "gemini-2.5-pro",
        contents: prompt,
        config: {
          responseMimeType: "application/json",
          responseSchema: combinedProfileResponseSchema,
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
        }
      });

      const responseText = response.text;
      if (!responseText) {
        throw new Error('Empty response from Gemini AI');
      }

      const profileData: CombinedProfileResponse = JSON.parse(responseText);

      // Validasi response
      if (!profileData.profileTitle || !profileData.profileDescription ||
          !profileData.strengths || !profileData.careerSuggestions ||
          !profileData.workStyle || !profileData.developmentAreas ||
          !profileData.personalityInsights || !profileData.careerFit) {
        throw new Error('Invalid response structure from Gemini AI');
      }

      return profileData;
    } catch (error) {
      console.error('Error generating combined profile with Gemini AI:', error);

      // Fallback ke response default jika AI gagal
      return this.getCombinedFallbackProfile();
    }
  }

  // Fallback profile untuk analisis gabungan
  private getCombinedFallbackProfile(): CombinedProfileResponse {

    return {
      profileTitle: 'Profil Kepribadian Terintegrasi',
      profileDescription: 'Anda memiliki kombinasi unik antara minat karier dan kepribadian yang dapat dikembangkan lebih lanjut. Profil ini menggabungkan aspek minat dan karakteristik kepribadian Anda.',
      strengths: [
        'Kemampuan adaptasi yang baik',
        'Potensi pengembangan yang beragam',
        'Fleksibilitas dalam pendekatan',
        'Keseimbangan antara minat dan kepribadian',
        'Kapasitas untuk pertumbuhan personal'
      ],
      careerSuggestions: [
        'Konsultan multidisiplin',
        'Koordinator proyek',
        'Spesialis pengembangan',
        'Analis sistem',
        'Manajer operasional'
      ],
      workStyle: 'Lingkungan kerja yang seimbang dengan variasi tugas dan interaksi yang disesuaikan dengan preferensi personal.',
      developmentAreas: [
        'Pengembangan keterampilan komunikasi',
        'Peningkatan kemampuan analitis',
        'Penguatan kepercayaan diri',
        'Pengembangan keterampilan teknis'
      ],
      personalityInsights: [
        'Memiliki potensi untuk berkembang di berbagai bidang',
        'Dapat menyesuaikan diri dengan berbagai situasi kerja',
        'Membutuhkan lingkungan yang mendukung pertumbuhan',
        'Cocok untuk peran yang memerlukan fleksibilitas'
      ],
      careerFit: 'Kombinasi profil Anda menunjukkan fleksibilitas dan adaptabilitas yang tinggi, cocok untuk karier yang memerlukan keseimbangan antara berbagai keterampilan dan kemampuan interpersonal.'
    };
  }
}
