import { OceanQuestion, OceanDescription } from './types';

// Big Five (OCEAN) Personality Questions (25 total - 5 per dimension)
export const oceanQuestions: OceanQuestion[] = [
  // Openness to Experience (O) - 5 questions
  {
    id: 31,
    text: "Saya memiliki imajinasi yang hidup dan penuh ide.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 32,
    text: "Saya tertarik pada ide-ide yang bersifat pemikiran mendalam.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 33,
    text: "Saya suka mencoba hal-hal baru atau mengunjungi tempat-tempat baru.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 34,
    text: "Saya tidak biasa dan sering memiliki cara berpikir yang berbeda.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 35,
    text: "Saya menikmati seni dan keindahan di sekitar saya.",
    ocean_type: 'O',
    isReversed: false
  },

  // Conscientiousness (C) - 5 questions
  {
    id: 36,
    text: "<PERSON>a selalu mempersiapkan diri dengan baik untuk segala sesuatu.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 37,
    text: "Saya sangat memperhatikan detail dan jarang membuat kesalahan.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 38,
    text: "Saya menyelesaikan tugas yang sudah saya mulai.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 39,
    text: "Saya suka membuat rencana dan jadwal yang teratur.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 40,
    text: "Saya adalah orang yang disiplin dan dapat diandalkan.",
    ocean_type: 'C',
    isReversed: false
  },

  // Extraversion (E) - 5 questions
  {
    id: 41,
    text: "Saya merasa bersemangat setelah menghabiskan waktu bersama banyak orang.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 42,
    text: "Saya nyaman menjadi pusat perhatian.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 43,
    text: "Saya mudah memulai percakapan dengan orang asing.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 44,
    text: "Saya adalah orang yang banyak bicara dan suka mengungkapkan perasaan.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 45,
    text: "Saya lebih suka bertindak daripada hanya berpikir dalam-dalam.",
    ocean_type: 'E',
    isReversed: false
  },

  // Agreeableness (A) - 5 questions
  {
    id: 46,
    text: "Saya mudah percaya pada orang lain.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 47,
    text: "Saya cenderung menghindari konflik dan mencari harmoni.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 48,
    text: "Saya merasakan rasa iba yang besar terhadap penderitaan orang lain.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 49,
    text: "Saya senang membantu orang lain, bahkan jika itu menyusahkan saya.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 50,
    text: "Saya jarang mengejek atau meremehkan orang lain.",
    ocean_type: 'A',
    isReversed: false
  },

  // Neuroticism (N) - 5 questions
  {
    id: 51,
    text: "Saya sering merasa cemas atau khawatir akan banyak hal.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 52,
    text: "Suasana hati saya bisa naik turun dengan cepat.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 53,
    text: "Saya mudah merasa stres atau terbebani.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 54,
    text: "Saya sering merasa tidak puas dengan diri sendiri.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 55,
    text: "Saya cenderung tetap tenang di bawah tekanan.",
    ocean_type: 'N',
    isReversed: true
  }
];

// OCEAN type descriptions
export const oceanDescriptions: OceanDescription[] = [
  {
    type: 'O',
    name: 'Openness to Experience',
    description: 'Keterbukaan terhadap pengalaman baru, kreativitas, dan keingintahuan intelektual. Orang dengan skor tinggi cenderung imajinatif, artistik, dan terbuka terhadap ide-ide baru.'
  },
  {
    type: 'C',
    name: 'Conscientiousness',
    description: 'Kecenderungan untuk terorganisir, bertanggung jawab, dan disiplin. Orang dengan skor tinggi cenderung dapat diandalkan, pekerja keras, dan berorientasi pada tujuan.'
  },
  {
    type: 'E',
    name: 'Extraversion',
    description: 'Kecenderungan untuk mencari stimulasi dari dunia luar dan berinteraksi dengan orang lain. Orang dengan skor tinggi cenderung sosial, energik, dan asertif.'
  },
  {
    type: 'A',
    name: 'Agreeableness',
    description: 'Kecenderungan untuk kooperatif, percaya, dan empati terhadap orang lain. Orang dengan skor tinggi cenderung ramah, altruistik, dan mudah bekerja sama.'
  },
  {
    type: 'N',
    name: 'Neuroticism',
    description: 'Kecenderungan untuk mengalami emosi negatif seperti kecemasan, depresi, dan ketidakstabilan emosi. Skor tinggi menunjukkan sensitivitas terhadap stres dan emosi negatif.'
  }
];

// Function to shuffle OCEAN questions randomly
export function getShuffledOceanQuestions(): OceanQuestion[] {
  const shuffled = [...oceanQuestions];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}



// Likert scale options for OCEAN (same as RIASEC)
export const oceanLikertOptions = [
  { value: 1, label: 'Sangat Tidak Setuju' },
  { value: 2, label: 'Tidak Setuju' },
  { value: 3, label: 'Netral' },
  { value: 4, label: 'Setuju' },
  { value: 5, label: 'Sangat Setuju' }
];
