import { Question, OceanQuestion } from '@/lib/types';

interface QuestionCardProps {
  question: Question | OceanQuestion;
  questionNumber: number;
  totalQuestions: number;
}

export default function QuestionCard({ question, questionNumber, totalQuestions }: QuestionCardProps) {
  return (
    <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <div className="bg-indigo-100 text-indigo-600 rounded-full w-8 h-8 flex items-center justify-center font-semibold text-sm mr-3">
            {questionNumber}
          </div>
          <span className="text-sm text-gray-500 uppercase tracking-wide font-medium">
            Pertanyaan {questionNumber} dari {totalQuestions}
          </span>
        </div>
        <h2 className="text-xl md:text-2xl font-semibold text-gray-800 leading-relaxed">
          {question.text}
        </h2>
      </div>
    </div>
  );
}
