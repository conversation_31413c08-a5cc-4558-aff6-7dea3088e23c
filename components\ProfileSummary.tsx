import { ProfileInterpretation, CombinedProfileInterpretation } from '@/lib/profileStore';

interface ProfileSummaryProps {
  profileInterpretation?: ProfileInterpretation | null;
  combinedProfile?: CombinedProfileInterpretation | null;
  isCombinedMode?: boolean;
}

export default function ProfileSummary({
  profileInterpretation,
  combinedProfile,
  isCombinedMode = false
}: ProfileSummaryProps) {
  // Determine which data to use
  const data = isCombinedMode ? combinedProfile : profileInterpretation;

  if (!data) {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl shadow-xl p-8 mb-8 text-white">
      <div className="text-center mb-6">
        <div className="inline-block bg-white/20 rounded-full p-4 mb-4">
          <span className="text-4xl">{isCombinedMode ? '🧠' : '🎯'}</span>
        </div>
        <h2 className="text-3xl font-bold mb-2">
          {data.profileTitle}
        </h2>
        <p className="text-indigo-100 text-lg leading-relaxed max-w-3xl mx-auto">
          {data.profileDescription}
        </p>
      </div>

      {isCombinedMode ? (
        // Combined mode layout
        <>
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            {/* Strengths */}
            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <span className="mr-2">💪</span>
                Kekuatan Utama
              </h3>
              <ul className="space-y-2">
                {data.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-yellow-300 mr-2 mt-1">✓</span>
                    <span className="text-indigo-100">{strength}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Career Suggestions */}
            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <span className="mr-2">🚀</span>
                Rekomendasi Karir
              </h3>
              <ul className="space-y-2">
                {data.careerSuggestions.slice(0, 5).map((career, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-yellow-300 mr-2 mt-1">•</span>
                    <span className="text-indigo-100">{career}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-6">
            {/* Personality Insights */}
            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <span className="mr-2">🧠</span>
                Insights Kepribadian
              </h3>
              <ul className="space-y-2">
                {combinedProfile?.personalityInsights.map((insight, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-yellow-300 mr-2 mt-1">💡</span>
                    <span className="text-indigo-100">{insight}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Development Areas */}
            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <span className="mr-2">📈</span>
                Area Pengembangan
              </h3>
              <ul className="space-y-2">
                {combinedProfile?.developmentAreas.map((area, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-yellow-300 mr-2 mt-1">🎯</span>
                    <span className="text-indigo-100">{area}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Work Style & Career Fit */}
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <span className="mr-2">🏢</span>
                Gaya Kerja Ideal
              </h3>
              <p className="text-indigo-100 leading-relaxed">
                {(data as CombinedProfileInterpretation).workStyle}
              </p>
            </div>

            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <span className="mr-2">🎯</span>
                Kesesuaian Karir
              </h3>
              <p className="text-indigo-100 leading-relaxed">
                {combinedProfile?.careerFit}
              </p>
            </div>
          </div>
        </>
      ) : (
        // RIASEC only mode layout
        <>
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            {/* Interests & Preferences */}
            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <span className="mr-2">❤️</span>
                Minat & Preferensi Utama
              </h3>
              <ul className="space-y-2">
                {data.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-yellow-300 mr-2 mt-1">✓</span>
                    <span className="text-indigo-100">{strength}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Career Suggestions */}
            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <span className="mr-2">🚀</span>
                Karir yang Mungkin Menarik
              </h3>
              <ul className="space-y-2">
                {data.careerSuggestions.slice(0, 5).map((career, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-yellow-300 mr-2 mt-1">•</span>
                    <span className="text-indigo-100">{career}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Work Environment */}
          <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
            <h3 className="text-xl font-semibold mb-3 flex items-center">
              <span className="mr-2">🏢</span>
              Lingkungan Kerja yang Disukai
            </h3>
            <p className="text-indigo-100 leading-relaxed">
              {(data as ProfileInterpretation).workEnvironment}
            </p>
          </div>
        </>
      )}

      {/* Call to Action */}
      <div className="text-center mt-6">
        <div className="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
          <p className="text-indigo-100 text-sm">
            💡 <strong>Catatan Penting:</strong> Hasil ini menggambarkan minat dan preferensi Anda, bukan kemampuan atau keahlian.
            Gunakan sebagai panduan untuk mengeksplorasi bidang yang menarik bagi Anda dan pertimbangkan untuk mengembangkan keterampilan yang diperlukan!
          </p>
        </div>
      </div>
    </div>
  );
}
