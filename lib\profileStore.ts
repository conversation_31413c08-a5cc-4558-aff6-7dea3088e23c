import { RiasecType, RiasecScores, OceanScores, OceanType } from './types';
import { GeminiProfileService, CombinedProfileResponse } from './geminiService';
import { oceanQuestions } from './oceanQuestions';



// Interface untuk profil interpretasi gabungan RIASEC + OCEAN
export interface CombinedProfileInterpretation {
  // RIASEC data
  riasecData: {
    scores: RiasecScores;
    dominantTypes: RiasecType[];
    level: string;
  };

  // OCEAN data
  oceanData: {
    scores: OceanScores;
    traits: OceanTraitSummary[];
    personalityType: string;
  };

  // Combined analysis
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workStyle: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
}

// Interface untuk ringkasan trait OCEAN
export interface OceanTraitSummary {
  trait: OceanType;
  name: string;
  score: number;
  level: 'Rendah' | 'Sedang' | 'Tinggi';
  description: string;
}



// Fungsi untuk mendapatkan level interpretasi skor
export function getScoreLevel(score: number): { level: string; color: string; description: string } {
  if (score >= 20) {
    return {
      level: 'Sangat Tinggi',
      color: 'text-green-600 bg-green-50 border-green-200',
      description: 'Minat yang sangat kuat pada area ini'
    };
  } else if (score >= 15) {
    return {
      level: 'Tinggi',
      color: 'text-blue-600 bg-blue-50 border-blue-200',
      description: 'Minat yang cukup kuat pada area ini'
    };
  } else if (score >= 10) {
    return {
      level: 'Sedang',
      color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
      description: 'Minat yang moderat pada area ini'
    };
  } else {
    return {
      level: 'Rendah',
      color: 'text-gray-600 bg-gray-50 border-gray-200',
      description: 'Minat yang rendah pada area ini'
    };
  }
}

// Fungsi untuk mendapatkan interpretasi profil gabungan RIASEC + OCEAN
export async function getCombinedProfileInterpretation(
  riasecScores: RiasecScores,
  oceanScores: OceanScores
): Promise<CombinedProfileInterpretation> {
  try {
    // Inisialisasi Gemini service
    const geminiService = new GeminiProfileService();

    // Generate combined profile menggunakan AI
    const aiProfile: CombinedProfileResponse = await geminiService.generateCombinedProfile(riasecScores, oceanScores);

    // Hitung dominant RIASEC types
    const sortedRiasecScores = Object.entries(riasecScores)
      .sort(([,a], [,b]) => b - a)
      .map(([type, score]) => ({ type: type as RiasecType, score }));

    const maxRiasecScore = sortedRiasecScores[0].score;
    const dominantRiasecTypes = sortedRiasecScores
      .filter(item => item.score === maxRiasecScore)
      .map(item => item.type);

    // Generate OCEAN trait summaries
    const oceanTraits: OceanTraitSummary[] = [
      {
        trait: 'O',
        name: 'Openness',
        score: oceanScores.O,
        level: getOceanTraitLevel(oceanScores.O),
        description: getOceanTraitDescription('O', getOceanTraitLevel(oceanScores.O))
      },
      {
        trait: 'C',
        name: 'Conscientiousness',
        score: oceanScores.C,
        level: getOceanTraitLevel(oceanScores.C),
        description: getOceanTraitDescription('C', getOceanTraitLevel(oceanScores.C))
      },
      {
        trait: 'E',
        name: 'Extraversion',
        score: oceanScores.E,
        level: getOceanTraitLevel(oceanScores.E),
        description: getOceanTraitDescription('E', getOceanTraitLevel(oceanScores.E))
      },
      {
        trait: 'A',
        name: 'Agreeableness',
        score: oceanScores.A,
        level: getOceanTraitLevel(oceanScores.A),
        description: getOceanTraitDescription('A', getOceanTraitLevel(oceanScores.A))
      },
      {
        trait: 'N',
        name: 'Neuroticism',
        score: oceanScores.N,
        level: getOceanTraitLevel(oceanScores.N),
        description: getOceanTraitDescription('N', getOceanTraitLevel(oceanScores.N))
      }
    ];

    // Determine personality type based on highest OCEAN scores
    const sortedOceanTraits = oceanTraits
      .sort((a, b) => b.score - a.score)
      .slice(0, 2);

    const personalityType = `${sortedOceanTraits[0].name} - ${sortedOceanTraits[1].name}`;

    return {
      riasecData: {
        scores: riasecScores,
        dominantTypes: dominantRiasecTypes,
        level: getRiasecLevelDescription(maxRiasecScore)
      },
      oceanData: {
        scores: oceanScores,
        traits: oceanTraits,
        personalityType
      },
      profileTitle: aiProfile.profileTitle,
      profileDescription: aiProfile.profileDescription,
      strengths: aiProfile.strengths,
      careerSuggestions: aiProfile.careerSuggestions,
      workStyle: aiProfile.workStyle,
      developmentAreas: aiProfile.developmentAreas,
      personalityInsights: aiProfile.personalityInsights,
      careerFit: aiProfile.careerFit
    };
  } catch (error) {
    console.error('Error getting combined AI profile interpretation:', error);

    // Fallback ke response default jika AI gagal
    return getCombinedFallbackProfile(riasecScores, oceanScores);
  }
}

// Helper functions untuk OCEAN trait analysis
export function getOceanTraitLevel(score: number): 'Rendah' | 'Sedang' | 'Tinggi' {
  // Skor OCEAN berkisar 5-25 (5 pertanyaan x 1-5 skala)
  if (score <= 12) return 'Rendah';
  if (score <= 18) return 'Sedang';
  return 'Tinggi';
}

export function getOceanTraitDescription(trait: string, level: 'Rendah' | 'Sedang' | 'Tinggi'): string {
  // Support both short keys (O, C, E, A, N) and full names
  const traitKey = trait.length === 1 ? trait : trait.charAt(0);

  const descriptions = {
    'O': {
      'Rendah': 'Cenderung praktis dan konvensional dalam pendekatan',
      'Sedang': 'Seimbang antara praktis dan terbuka terhadap ide baru',
      'Tinggi': 'Sangat kreatif, imajinatif, dan terbuka terhadap pengalaman baru'
    },
    'C': {
      'Rendah': 'Cenderung fleksibel dan spontan dalam pendekatan',
      'Sedang': 'Seimbang antara terorganisir dan fleksibel',
      'Tinggi': 'Sangat terorganisir, disiplin, dan berorientasi pada tujuan'
    },
    'E': {
      'Rendah': 'Cenderung introspektif dan menyukai ketenangan',
      'Sedang': 'Seimbang antara sosial dan waktu pribadi',
      'Tinggi': 'Sangat sosial, energik, dan mencari stimulasi eksternal'
    },
    'A': {
      'Rendah': 'Cenderung kompetitif dan skeptis',
      'Sedang': 'Seimbang antara kooperatif dan asertif',
      'Tinggi': 'Sangat kooperatif, empati, dan mudah percaya'
    },
    'N': {
      'Rendah': 'Sangat stabil secara emosional dan tenang',
      'Sedang': 'Cukup stabil dengan sesekali mengalami stres',
      'Tinggi': 'Sensitif terhadap stres dan emosi negatif'
    }
  };

  return descriptions[traitKey as keyof typeof descriptions]?.[level] || 'Deskripsi tidak tersedia';
}

function getRiasecLevelDescription(score: number): string {
  if (score >= 20) return 'Sangat Tinggi';
  if (score >= 15) return 'Tinggi';
  if (score >= 10) return 'Sedang';
  return 'Rendah';
}

// Fallback profile untuk analisis gabungan
function getCombinedFallbackProfile(riasecScores: RiasecScores, oceanScores: OceanScores): CombinedProfileInterpretation {
  const maxRiasecScore = Math.max(...Object.values(riasecScores));
  const dominantRiasecType = Object.entries(riasecScores)
    .find(([, score]) => score === maxRiasecScore)?.[0] as RiasecType || 'R';

  const oceanTraits: OceanTraitSummary[] = [
    {
      trait: 'O',
      name: 'Openness',
      score: oceanScores.O,
      level: getOceanTraitLevel(oceanScores.O),
      description: getOceanTraitDescription('O', getOceanTraitLevel(oceanScores.O))
    },
    {
      trait: 'C',
      name: 'Conscientiousness',
      score: oceanScores.C,
      level: getOceanTraitLevel(oceanScores.C),
      description: getOceanTraitDescription('C', getOceanTraitLevel(oceanScores.C))
    },
    {
      trait: 'E',
      name: 'Extraversion',
      score: oceanScores.E,
      level: getOceanTraitLevel(oceanScores.E),
      description: getOceanTraitDescription('E', getOceanTraitLevel(oceanScores.E))
    },
    {
      trait: 'A',
      name: 'Agreeableness',
      score: oceanScores.A,
      level: getOceanTraitLevel(oceanScores.A),
      description: getOceanTraitDescription('A', getOceanTraitLevel(oceanScores.A))
    },
    {
      trait: 'N',
      name: 'Neuroticism',
      score: oceanScores.N,
      level: getOceanTraitLevel(oceanScores.N),
      description: getOceanTraitDescription('N', getOceanTraitLevel(oceanScores.N))
    }
  ];

  return {
    riasecData: {
      scores: riasecScores,
      dominantTypes: [dominantRiasecType],
      level: getRiasecLevelDescription(maxRiasecScore)
    },
    oceanData: {
      scores: oceanScores,
      traits: oceanTraits,
      personalityType: 'Profil Kepribadian Seimbang'
    },
    profileTitle: 'Profil Kepribadian dan Minat Terintegrasi',
    profileDescription: 'Anda memiliki kombinasi unik antara minat karier dan kepribadian yang dapat dikembangkan lebih lanjut untuk mencapai potensi maksimal.',
    strengths: [
      'Kemampuan adaptasi yang baik',
      'Potensi pengembangan yang beragam',
      'Fleksibilitas dalam pendekatan',
      'Keseimbangan antara minat dan kepribadian',
      'Kapasitas untuk pertumbuhan personal'
    ],
    careerSuggestions: [
      'Konsultan multidisiplin',
      'Koordinator proyek',
      'Spesialis pengembangan',
      'Analis sistem',
      'Manajer operasional'
    ],
    workStyle: 'Lingkungan kerja yang seimbang dengan variasi tugas dan interaksi yang disesuaikan dengan preferensi personal.',
    developmentAreas: [
      'Pengembangan keterampilan komunikasi',
      'Peningkatan kemampuan analitis',
      'Penguatan kepercayaan diri',
      'Pengembangan keterampilan teknis'
    ],
    personalityInsights: [
      'Memiliki potensi untuk berkembang di berbagai bidang',
      'Dapat menyesuaikan diri dengan berbagai situasi kerja',
      'Membutuhkan lingkungan yang mendukung pertumbuhan',
      'Cocok untuk peran yang memerlukan fleksibilitas'
    ],
    careerFit: 'Kombinasi profil Anda menunjukkan fleksibilitas dan adaptabilitas yang tinggi, cocok untuk karier yang memerlukan keseimbangan antara berbagai keterampilan dan kemampuan interpersonal.'
  };
}

// Function to calculate OCEAN scores from answers
export function calculateOceanScores(answers: Record<number, number>): OceanScores {
  const scores: OceanScores = { O: 0, C: 0, E: 0, A: 0, N: 0 };

  oceanQuestions.forEach(question => {
    const answer = answers[question.id];
    if (answer !== undefined) {
      // For reversed questions, flip the score (1->5, 2->4, 3->3, 4->2, 5->1)
      const adjustedScore = question.isReversed ? (6 - answer) : answer;
      scores[question.ocean_type] += adjustedScore;
    }
  });

  return scores;
}
